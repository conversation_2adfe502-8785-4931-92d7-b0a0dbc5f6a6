{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AppRegistration": {
    "TenantId": "30350f35-feb0-4bbc-877f-43ff406a41e5",
    "ClientId": "979f2e72-721b-4313-9b07-513b5bb8fbcc",
    "ClientSecret": "****************************************"
  },
  "Providers": {
    "CxPay": {
      "ProviderKey": "CXPAYKEY"
    }
  },
  // This is the domain that is checked when emailaddresses are restricted to the office domain
  "TrustOffice": {
    "EmailDomain": "contoso.com",
    "ClientPortalUrl": "https://clientfilings.netprodevelopment.com"
  },
  "BlobStorage": {
    "AccountName": "sadevpcpeus2",
    "ContainerName": "documents"
  },
  "Azure": {
    "MSGraph": {
      "AD": {
        // Use defaults from AppRegistration
        "Scopes": ".default"
      },
      "B2C": {
        "TenantId": "f878cfb6-eda4-4757-a39f-e0ceb150b904",
        "ClientId": "5f142843-7f78-4fa7-b10e-7ce13d3bd381",
        "ClientSecret": "****************************************",
        "Scopes": ".default"
      }
    }
  },
  "FeatureFlags": {
    "Announcements": true
  }
}
