﻿// <copyright file="WellKnownSTRPermissionNames.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Permissions
{
    /// <summary>
    /// A list of the known permissions for the STR (Simple Tax Return) module.
    /// </summary>
    public static partial class WellKnownPermissionNames
    {
#pragma warning disable SA1310 // Field names should not contain underscore

        /// <summary>
        /// View STR submissions.
        /// </summary>
        public const string STRModule_Submissions_View = STRModule_Submissions + ".view";

        /// <summary>
        /// Search STR submissions.
        /// </summary>
        public const string STRModule_Submissions_Search = STRModule_Submissions + ".search";

        /// <summary>
        /// Export STR Submissions (download summary).
        /// </summary>
        public const string STRModule_Submissions_Export = STRModule_Submissions + ".export";

        /// <summary>
        /// Reset STR submissions to Saved/Re-open.
        /// </summary>
        public const string STRModule_Submissions_Reset = STRModule_Submissions + ".reset";

        /// <summary>
        /// View paid/unpaid.
        /// </summary>
        public const string STRModule_Submissions_View_Paid = STRModule_Submissions + ".view-paid";

        /// <summary>
        /// Mark as paid.
        /// </summary>
        public const string STRModule_Submissions_Mark_Paid = STRModule_Submissions + ".mark-paid";

        /// <summary>
        /// Import payments.
        /// </summary>
        public const string STRModule_Payments_Import = STRModule + ".payments.import";

        /// <summary>
        /// Export submissions to IRD.
        /// </summary>
        public const string STRModule_Submissions_Export_IRD = STRModule_Submissions + ".export.ird";

        /// <summary>
        /// Export invoices (download submission invoice).
        /// </summary>
        public const string STRModule_Invoices_Export = STRModule + ".invoices.export";

        /// <summary>
        /// Export invoices (Financial Report).
        /// </summary>
        public const string STRModule_Invoices_Export_Financial = STRModule + ".invoices.export.financial-report";

        /// <summary>
        /// Management information.
        /// </summary>
        public const string STRModule_Management_Information = STRModule + ".management-information";

        /// <summary>
        /// View STR Fee.
        /// </summary>
        public const string STRModule_View_Fee = STRModule + ".fee.view";

        /// <summary>
        /// Set STR Fee.
        /// </summary>
        public const string STRModule_Set_Fee = STRModule + ".fee.set";

        /// <summary>
        /// View late payments.
        /// </summary>
        public const string STRModule_View_Late_Payments = STRModule + ".late-payments.view";

        /// <summary>
        /// Set late payments.
        /// </summary>
        public const string STRModule_Set_Late_Payments = STRModule + ".late-payments.set";

        /// <summary>
        /// View and run the data migration.
        /// </summary>
        public const string STRModule_DataMigration = STRModule + ".data-migration";

        /// <summary>
        /// Edit late payment settings.
        /// </summary>
        public const string STRModule_Edit_Late_Payments = STRModule + ".late-payments.edit";

        /// <summary>
        /// View STR submission log.
        /// </summary>
        public const string STRModule_View_Submission_Log = STRModule + ".submission-log.view";

        /// <summary>
        /// Request for information start.
        /// </summary>
        public const string STRModule_Start_RFI_Request = STRModule + ".rfi-request.start";

        private const string STRModule = "str";
        private const string STRModule_Submissions = STRModule + ".submissions";

#pragma warning restore SA1310 // Field names should not contain underscore
    }
}
