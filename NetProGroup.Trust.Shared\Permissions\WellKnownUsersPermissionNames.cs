﻿// <copyright file="WellKnownUsersPermissionNames.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Permissions
{
    /// <summary>
    /// A list of the known permissions for users.
    /// </summary>
#pragma warning disable SA1202 // Elements should be ordered by access
#pragma warning disable SA1310 // Field names should not contain underscore
    public static partial class WellKnownPermissionNames
    {
        /// <summary>
        /// Search Users.
        /// </summary>
        public const string Users_Search = Users + ".search";

        /// <summary>
        /// View Users.
        /// </summary>
        public const string Users_View = Users + ".view";

        /// <summary>
        /// Block Users.
        /// </summary>
        public const string Users_Block = Users + ".block";

        /// <summary>
        /// Unblock Users.
        /// </summary>
        public const string Users_Unblock = Users + ".unblock";

        /// <summary>
        /// Reset authentication for Users.
        /// </summary>
        public const string Users_ResetAuthentication = Users + ".reset-authentication";

        /// <summary>
        /// View the log for users.
        /// </summary>
        public const string Users_ViewLog = Users + ".view-log";

        private const string Users = "users";
    }
#pragma warning restore SA1310 // Field names should not contain underscore
#pragma warning restore SA1202 // Elements should be ordered by access
}
