// <copyright file="SearchMasterClientsRequest.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.DataManager.MasterClients.RequestResponses
{
    /// <summary>
    /// Request model for searching MasterClients.
    /// </summary>
    public class SearchMasterClientsRequest
    {
        /// <summary>
        /// Gets or sets the id of the user to search the master clients for.
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// Gets or sets the search term.
        /// </summary>
        public string SearchTerm { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to include the list of legal entities for each MasterClient.
        /// </summary>
        public bool IncludeLegalEntities { get; set; }
    }
}
