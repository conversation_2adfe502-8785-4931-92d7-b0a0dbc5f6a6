﻿using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.ActivityLogs.EFModels;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Trust.Application.Contracts.LegalEntities;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Companies;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.Domain.Currencies;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Shared.Enums;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Tests.Shared;
using MasterClient = NetProGroup.Trust.Domain.MasterClients.MasterClient;

namespace NetProGroup.Trust.Tests.AppServices.LegalEntities
{
    [TestFixture()]
    public class LegalEntitiesAppServiceTests : TestBase
    {
        private ILegalEntitiesAppService _service;
        private IJurisdictionsRepository _jurisdictionsRepository;
        private ILegalEntitiesRepository _legalEntitiesRepository;
        private IMasterClientsRepository _masterClientsRepository;
        private ISystemAuditManager _systemAuditManager;
        private IInvoiceRepository _invoiceRepository;

        [SetUp]
        public void SetUp()
        {
            _service = _server.Services.GetRequiredService<ILegalEntitiesAppService>();
            _jurisdictionsRepository = _server.Services.GetRequiredService<IJurisdictionsRepository>();
            _legalEntitiesRepository = _server.Services.GetRequiredService<ILegalEntitiesRepository>();
            _masterClientsRepository = _server.Services.GetRequiredService<IMasterClientsRepository>();
            _systemAuditManager = _server.Services.GetRequiredService<ISystemAuditManager>();
            _invoiceRepository = _server.Services.GetRequiredService<IInvoiceRepository>();
        }

        private async Task<LegalEntity> CreateTestCompany(string name, Jurisdiction jurisdiction)
        {
            var masterClient = _masterClientsRepository.GetQueryable().FirstOrDefault();
            if (masterClient == null)
            {
                masterClient = new MasterClient(Guid.NewGuid())
                {
                    Code = "TEST_123"
                };
                await _masterClientsRepository.InsertAsync(masterClient, true);
            }

            var testLegalEntity = new LegalEntity(Guid.NewGuid())
            {
                Name = name,
                Code = name,
                EntityType = LegalEntityType.Company,
                MasterClient = masterClient,
                Jurisdiction = jurisdiction,
                EntityTypeName = LegalEntityTypes.IBC,
            };


            await _legalEntitiesRepository.InsertAsync(testLegalEntity, true);

            return testLegalEntity;
        }

        private Jurisdiction CreateJurisdictionIfNotExists(string name)
        {
            var jurisdiction = _jurisdictionsRepository.FindFirstOrDefaultByCondition(j => j.Name == name);

            if (jurisdiction == null)
            {
                jurisdiction = new Jurisdiction(Guid.NewGuid())
                {
                    Name = name,
                    Code = name
                };

                _jurisdictionsRepository.Insert(jurisdiction, true);
            }
            return jurisdiction;
        }

        [Test()]
        public async Task ListCompaniesAsync_PassesSecurityManagerResultToDataManager()
        {
            // Arrange
            var jurisdiction1 = CreateJurisdictionIfNotExists("Nevis");
            var jurisdiction2 = CreateJurisdictionIfNotExists("Panama");

            var company1 = await CreateTestCompany("Nevis company", jurisdiction1);
            var company2 = await CreateTestCompany("Panama company", jurisdiction2);

            var nevisOwner = ManagementUser;

            var workContext = _server.Services.GetRequiredService<IWorkContext>();
            workContext.IdentityUserId = nevisOwner!.Id;

            // Act
            var result = await _service.ListCompaniesAsync(null, null, null, null, 1, 10, null, null);

            // Assert
            result.Should().HaveCount(1);
            result.Single().Id.Should().Be(company1.Id);
            result.Should().NotContain(company => company.Id == company2.Id);
        }

        [Test()]
        public async Task SearchCompaniesWithAnnualFeeStatusAsync_PassesSecurityManagerResultToDataManager()
        {
            // Arrange
            var financialYear = 2023;
            var pageNumber = 1;
            var pageSize = 10;

            var jurisdiction1 = CreateJurisdictionIfNotExists("Nevis");
            var jurisdiction2 = CreateJurisdictionIfNotExists("Panama");

            var company1 = await CreateTestCompany("Nevis company", jurisdiction1);
            var company2 = await CreateTestCompany("Panama company", jurisdiction2);

            var nevisOwner = ManagementUser;

            var workContext = _server.Services.GetRequiredService<IWorkContext>();
            workContext.IdentityUserId = nevisOwner!.Id;

            // Act
            var result = await _service.SearchCompaniesWithAnnualFeeStatusAsync(financialYear, false, null, pageNumber, pageSize);

            // Assert
            result.Should().HaveCount(1);
            result.Single().CompanyId.Should().Be(company1.Id);
            result.Should().NotContain(company => company.CompanyId == company2.Id);
        }

        [Test]
        [TestCase(null, false, Description = "No existing annual fee, no change in status")]
        [TestCase(null, true, Description = "No existing annual fee, setting to true")]
        [TestCase(false, false, Description = "Existing annual fee is false, no change")]
        [TestCase(true, true, Description = "Existing annual fee is true, no change")]
        [TestCase(false, true, Description = "Existing annual fee is false, changing to true")]
        [TestCase(true, false, Description = "Existing annual fee is true, changing to false")]
        public async Task SetCompanyAnnualFeeStatusAsync_OnlyWritesActivityLog_WhenStatusChanges(
            bool? existingIsPaidValue,
            bool newIsPaidValue)
        {
            // Arrange
            var jurisdiction = CreateJurisdictionIfNotExists("Nevis");
            var company = await CreateTestCompany("Test Company", jurisdiction);

            // Set up the user context for authorization
            var userManager = _server.Services.GetRequiredService<IUserManager>();
            var nevisOwner = userManager.GetUsersByRoleNameAsync(WellKnownRoleNames.Nevis_Owner).Result.FirstOrDefault();
            var workContext = _server.Services.GetRequiredService<IWorkContext>();
            workContext.IdentityUserId = nevisOwner!.Id;

            const int testYear = 2024;

            // Set up existing annual fee if needed
            if (existingIsPaidValue.HasValue)
            {
                company.UpdateAnnualFeeStatus(testYear, existingIsPaidValue.Value);
                await _legalEntitiesRepository.UpdateAsync(company, true);
            }

            // Get initial activity log count
            var initialLogs = await ListActivityLogsAsync();

            // Act
            var model = new SetCompanyAnnualFeesDTO
            {
                CompanyId = company.Id,
                AnnualFees = [new() { FinancialYear = testYear, IsPaid = newIsPaidValue }]
            };

            await _service.SetCompanyAnnualFeeStatusAsync(model);

            // Assert
            var finalLogs = await ListActivityLogsAsync();

            var newLogs = finalLogs.Except(initialLogs);
            if (existingIsPaidValue.GetValueOrDefault() != newIsPaidValue)
            {
                var newLog = newLogs.Should().ContainSingle().Which;
                newLog.ShortDescription.Should().Be($"Annual Fee status for {testYear} updated.");
            }
            else
            {
                newLogs.Should().BeEmpty();
            }

            return;

            async Task<List<ActivityLog>> ListActivityLogsAsync()
            {
                return await _systemAuditManager.ListActivityLogsAsync(
                    new List<Guid> { company.Id },
                    new List<string> { ActivityLogActivityTypes.CompanyAnnualFeeStatusUpdated });
            }
        }

        [Test]
        public async Task ResetCompoanyStatusToOnboardingAsync_WithApprovedOnboardingStatus_SetsToOnboarding()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var jurisdiction = CreateJurisdictionIfNotExists("Nevis");
            var company = await CreateTestCompany("Test Company", jurisdiction);
            company.ApproveOnboarding();
            await _legalEntitiesRepository.UpdateAsync(company, true);

            // Act
            await _service.ResetCompanyToOnboardingAsync(company.Id);

            // Assert
            var updatedCompany = await _legalEntitiesRepository.GetByIdAsync(company.Id);
            updatedCompany.OnboardingStatus.Should().Be(OnboardingStatus.Onboarding);
        }

        [Test]
        public async Task ResetCompoanyStatusToOnboardingAsync_WithNonApprovedOnboardingStatus_ThrowsException()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var jurisdiction = CreateJurisdictionIfNotExists("Nevis");
            var company = await CreateTestCompany("Test Company", jurisdiction);
            await _legalEntitiesRepository.UpdateAsync(company, true);

            // Act
            Func<Task> act = async () => await _service.ResetCompanyToOnboardingAsync(company.Id);

            // Assert
            await act.Should().ThrowAsync<InvalidOperationException>()
                .WithMessage("Can only reset to onboarding from approved status.");
        }

        [Test]
        public async Task ResetCompoanyStatusToOnboardingAsync_WithSubmissions_ThrowsException()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var jurisdiction = CreateJurisdictionIfNotExists("Nevis");
            var company = await CreateTestCompany("Test Company", jurisdiction);
            company.ApproveOnboarding();
            
            company.Submissions.Add(new Submission
            {
                ModuleId = ModuleStrId,
                FinancialYear = 2025,
                Layout = LayoutConsts.TridentTrust,
                Name = "Submission",
                ReportId = "Test"
            });

            await _legalEntitiesRepository.UpdateAsync(company, true);

            // Act
            Func<Task> act = async () => await _service.ResetCompanyToOnboardingAsync(company.Id);

            // Assert
            await act.Should().ThrowAsync<PreconditionFailedException>()
                .WithMessage("Companies with submissions cannot be reset to 'Onboarding'");
        }

        [Test]
        public async Task ResetCompoanyStatusToOnboardingAsync_WithInvoice_ThrowsException()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var jurisdiction = CreateJurisdictionIfNotExists("Nevis");
            var company = await CreateTestCompany("Test Company", jurisdiction);
            company.ApproveOnboarding();
            await _legalEntitiesRepository.UpdateAsync(company, true);

            var currency = new Currency(
                id: Guid.NewGuid(),
                name: "US Dollar",
                symbol: "$",
                code: "USD"
            );

            var invoice = new Invoice(Guid.NewGuid())
            {
                InvoiceNr = "INV-001",
                Date = DateTime.UtcNow,
                Amount = 100m,
                Status = InvoiceStatus.Pending,
                CurrencyId = currency.Id,
                Currency = currency,
                LegalEntityId = company.Id,
                LegalEntity = company,
                Layout = LayoutConsts.TridentTrust
            };

            await _invoiceRepository.InsertAsync(invoice, true);

            // Act
            Func<Task> act = async () => await _service.ResetCompanyToOnboardingAsync(company.Id);

            // Assert
            await act.Should().ThrowAsync<PreconditionFailedException>()
                .WithMessage("Companies with invoices cannot be reset to 'Onboarding'");
        }

        [Test]
        public async Task ResetCompoanyStatusToOnboardingAsync_UserWithNoPermission_ThrowsException()
        {
            // Arrange
            SetWorkContextUser(ClientUser);

            // Act
            Func<Task> act = async () => await _service.ResetCompanyToOnboardingAsync(Guid.NewGuid());

            // Assert
            await act.Should().ThrowAsync<ForbiddenException>()
                .WithMessage("The user is not a management user");
        }
    }
}