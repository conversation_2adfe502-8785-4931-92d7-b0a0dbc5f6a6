﻿// <copyright file="WellKnownMasterClientPermissionNames.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Permissions
{
    /// <summary>
    /// A list of the known permissions for master clients.
    /// </summary>
    public static partial class WellKnownPermissionNames
    {
        /// <summary>
        /// Search MasterClients.
        /// </summary>
#pragma warning disable SA1310 // Field names should not contain underscore
        public const string MasterClients_Search = MasterClients + ".search";

        /// <summary>
        /// View MasterClient.
        /// </summary>
        public const string MasterClients_View = MasterClients + ".view";

        /// <summary>
        /// Sent invitations for MasterClients.
        /// </summary>
        public const string MasterClients_SendInvitation = MasterClients + ".send-invitation";

        /// <summary>
        /// View TridentTrust users.
        /// </summary>
        public const string MasterClients_View_Trident_Users = MasterClients + TridentUsers + ".view";

        /// <summary>
        /// Add TridentTrust users.
        /// </summary>
        public const string MasterClients_Add_Trident_Users = MasterClients + TridentUsers + ".add";

        /// <summary>
        /// Remove TridentTrust users.
        /// </summary>
        public const string MasterClients_Remove_Trident_Users = MasterClients + TridentUsers + ".remove";

        /// <summary>
        /// View mastermlient log.
        /// </summary>
        public const string MasterClients_View_Log = MasterClients + ".log.view";
#pragma warning restore SA1310 // Field names should not contain underscore

        private const string MasterClients = "masterclients";
        private const string TridentUsers = ".trident-users";
    }
}
