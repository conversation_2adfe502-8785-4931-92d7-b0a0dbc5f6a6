﻿using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Paging;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Forms;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Shared.Constants;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Trust.Shared.FormDocuments;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.Managers
{
    public class Test_SubmissionManager : TestBase
    {
        private IMasterClientsRepository _masterClientsRepository;
        private ILegalEntitiesRepository _legalEntitiesRepository;
        private IFormDocumentsRepository _formDocumentsRepository;
        private IFormDocumentRevisionsRepository _formDocumentRevisionsRepository;
        private ISubmissionsManager _submissionsManager;
        private ISettingsManager _settingsManager;

        private Guid _nevisLegalEntityId; // Nevis legal entity
        private Guid _nevisLegalEntity2Id; // Nevis legal entity 2
        private Guid _bahamasLegalEntityId; // Bahamas legal entity
        private Guid _panamaLegalEntityId; // Panama legal entity
        private ISubmissionsRepository _submissionsRepository;
        private readonly DateTime _nevisLegalEntity2IncorporationDate = new DateTime(1989, 05, 22);
        private readonly string _nevisLegalEntity2IncorporationNr = "654321";
        private readonly DateTime _submissionStartAt = new DateTime(2024, 1, 1);
        private readonly DateTime _submissionEndAt = new DateTime(2024, 12, 31);
        private readonly string _nevisLegalEntity2VpCode = "2";
        private readonly string _nevisLegalEntity2Name = "Company 2";
        private readonly string _nevisLegalEntity2ReferralOffice = "referral office 2";
        private readonly string _nevisLegalEntity2LegacyCode = "Nevis-123";
        private readonly string _masterClientCode = "MCC 1";

        // Bahamas legal entity fields
        private readonly string _bahamasLegalEntityName = "Bahamas Company";
        private readonly string _bahamasLegalEntityCode = "3";
        private readonly string _bahamasLegalEntityIncorporationNr = "789012";
        private readonly string _bahamasLegalEntityReferralOffice = "bahamas office";
        private readonly DateTime _bahamasLegalEntityIncorporationDate = new DateTime(1995, 03, 15);
        private readonly string _bahamasLegalEntityLegacyCode = "Bahamas-456";

        // Panama legal entity fields
        private readonly string _panamaLegalEntityName = "Panama Company";
        private readonly string _panamaLegalEntityCode = "4";
        private readonly string _panamaLegalEntityIncorporationNr = "345678";
        private readonly string _panamaLegalEntityReferralOffice = "panama office";
        private readonly DateTime _panamaLegalEntityIncorporationDate = new DateTime(2000, 01, 10);
        private readonly string _panamaLegalEntityLegacyCode = "Panama-789";

        [SetUp]
        public void Setup()
        {
            _server.Services.GetRequiredService<IFormsDataManager>();

            _masterClientsRepository = _server.Services.GetRequiredService<IMasterClientsRepository>();
            _legalEntitiesRepository = _server.Services.GetRequiredService<ILegalEntitiesRepository>();
            _formDocumentsRepository = _server.Services.GetRequiredService<IFormDocumentsRepository>();
            _formDocumentRevisionsRepository = _server.Services.GetRequiredService<IFormDocumentRevisionsRepository>();
            _submissionsRepository = _server.Services.GetRequiredService<ISubmissionsRepository>();

            _submissionsManager = _server.Services.GetRequiredService<ISubmissionsManager>();
            _settingsManager = _server.Services.GetRequiredService<ISettingsManager>();
            Seed();
        }

        private void Seed()
        {
            // Get the client user
            var user = ClientUser;
            SetWorkContextUser(user);

            // Create a masterClient
            var masterClient = new MasterClient
            {
                Code = _masterClientCode
            };
            masterClient.MasterClientUsers.Add(new MasterClientUser { UserId = user.Id });
            _masterClientsRepository.Insert(masterClient, true);

            // Create a legal entity
            var legalEntity = new LegalEntity
            {
                MasterClientId = masterClient.Id,
                Name = "Company 1",
                Code = "1",
                JurisdictionId = JurisdictionNevisId,
                EntityType = LegalEntityType.Company,
                IncorporationNr = "123456",
                ReferralOffice = "referral office"
            };
            _legalEntitiesRepository.Insert(legalEntity, true);
            _nevisLegalEntityId = legalEntity.Id;

            // Create a second legal entity
            var legalEntity2 = new LegalEntity
            {
                MasterClientId = masterClient.Id,
                Name = _nevisLegalEntity2Name,
                Code = _nevisLegalEntity2VpCode,
                LegacyCode = _nevisLegalEntity2LegacyCode,
                JurisdictionId = JurisdictionNevisId,
                EntityType = LegalEntityType.Company,
                IncorporationNr = _nevisLegalEntity2IncorporationNr,
                ReferralOffice = _nevisLegalEntity2ReferralOffice,
                IncorporationDate = _nevisLegalEntity2IncorporationDate
            };
            _legalEntitiesRepository.Insert(legalEntity2, true);
            _nevisLegalEntity2Id = legalEntity2.Id;

            // Create a legal entity for Bahamas
            var bahamasLegalEntity = new LegalEntity
            {
                MasterClientId = masterClient.Id,
                Name = _bahamasLegalEntityName,
                Code = _bahamasLegalEntityCode,
                LegacyCode = _bahamasLegalEntityLegacyCode,
                JurisdictionId = JurisdictionBahamasId,
                EntityType = LegalEntityType.Company,
                IncorporationNr = _bahamasLegalEntityIncorporationNr,
                ReferralOffice = _bahamasLegalEntityReferralOffice,
                IncorporationDate = _bahamasLegalEntityIncorporationDate
            };
            _legalEntitiesRepository.Insert(bahamasLegalEntity, true);
            _bahamasLegalEntityId = bahamasLegalEntity.Id;

            // Create a legal entity for Panama
            var panamaLegalEntity = new LegalEntity
            {
                MasterClientId = masterClient.Id,
                Name = _panamaLegalEntityName,
                Code = _panamaLegalEntityCode,
                LegacyCode = _panamaLegalEntityLegacyCode,
                JurisdictionId = JurisdictionPanamaId,
                EntityType = LegalEntityType.Company,
                IncorporationNr = _panamaLegalEntityIncorporationNr,
                ReferralOffice = _panamaLegalEntityReferralOffice,
                IncorporationDate = _panamaLegalEntityIncorporationDate
            };
            _legalEntitiesRepository.Insert(panamaLegalEntity, true);
            _panamaLegalEntityId = panamaLegalEntity.Id;
        }

        [Test]
        public async Task Test_Start_Submission_Success()
        {
            // Arrange
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };

            // Act
            var response = await _submissionsManager.StartSubmissionAsync(model);

            // Assert
            Assert.That(response.Status, Is.EqualTo(SubmissionStatus.Draft));
            response.Layout.Should().Be(LayoutConsts.TridentTrust);
        }

        [Test]
        public async Task Test_Start_Submission_Twice_fails()
        {
            // Arrange
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };

            // Act
            await _submissionsManager.StartSubmissionAsync(model);


            // Assert
            Assert.ThrowsAsync<ConstraintException>(async () => await _submissionsManager.StartSubmissionAsync(model))?
                  .Message.Should().Contain("A submission for this company/module already exists for year");
        }

        [Test]
        public async Task Test_Update_Submission_Success()
        {
            // Arrange
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var response1 = await _submissionsManager.StartSubmissionAsync(model);

            // Arrange 2
            var form = response1.FormDocument.Revisions.First().FormBuilder;
            var modelUpdate = new SubmissionDataSetDTO { Id = response1.Id, DataSet = (form.Form as KeyValueForm)?.DataSet };

            // Act
            var response = await _submissionsManager.UpdateSubmissionDataSetAsync(modelUpdate);

            // Assert
            Assert.That(response.Status, Is.EqualTo(SubmissionStatus.Draft));
        }

        [Test]
        public async Task Test_Submit_Submission_Success()
        {
            // Arrange
            var feeSettings = new FeeSettingsDTO
            {
                STRSubmissionFee = 150,
                STRSubmissionFeeInvoiceText = "Submission fee for year {year}"
            };
            await _settingsManager.SaveSettingsForJurisdictionAsync(feeSettings, JurisdictionNevisId);

            var numberingSettings = new InvoiceNumberingSettingsDTO
            {
                InitialNumber = 4000,
                PrefixFormat = "{yy}{mm}",
                RangeFormat = "{yy}{mm}",
                FullInvoiceNumberFormat = "{prefix}/{number}"
            };
            await _settingsManager.SaveInvoiceNumberingSettingsAsync(JurisdictionNevisId, ModuleStrId, numberingSettings);

            var lastInvoiceNumberData = new Domain.SettingsModels.InvoiceNumberData
            {
                RangeIdentifier = "2405",
                InvoiceNumber = 415
            };
            await _settingsManager.SetLastInvoiceNumberAsync(JurisdictionNevisId, ModuleStrId, lastInvoiceNumberData);


            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var response1 = await _submissionsManager.StartSubmissionAsync(model);

            // Arrange 2
            var modelSubmit = new SubmitSubmissionDTO { SubmissionId = response1.Id };

            // Act
            var response = await _submissionsManager.SubmitSubmissionAsync(modelSubmit);

            // Assert
            Assert.That(response.Status, Is.EqualTo(SubmissionStatus.Submitted));

            var savedSubmission = await _formDocumentRevisionsRepository.FindFirstOrDefaultByConditionAsync(revision => true);
            var form = savedSubmission.GetFormBuilder().Form as KeyValueForm;
            Assert.IsNotNull(form);
            Assert.That(form.DataSet.ContainsKey("legal-entity-data.name"));
            Assert.That(form.DataSet["legal-entity-data.name"], Is.EqualTo("Company 1"));
            Assert.That(form.DataSet.ContainsKey("legal-entity-data.code"));
            Assert.That(form.DataSet["legal-entity-data.code"], Is.EqualTo("1"));
            Assert.That(form.DataSet.ContainsKey("legal-entity-data.incorporationNr"));
            Assert.That(form.DataSet["legal-entity-data.incorporationNr"], Is.EqualTo("123456"));
            Assert.That(form.DataSet.ContainsKey("legal-entity-data.masterClientCode"));
            Assert.That(form.DataSet["legal-entity-data.masterClientCode"], Is.EqualTo(_masterClientCode));
            Assert.That(form.DataSet.ContainsKey("legal-entity-data.referralOffice"));
            Assert.That(form.DataSet["legal-entity-data.referralOffice"], Is.EqualTo("referral office"));
            Assert.That(form.DataSet.ContainsKey("legal-entity-data.strSubmissionFee"));
            Assert.That(form.DataSet["legal-entity-data.strSubmissionFee"], Is.EqualTo("150"));
            Assert.That(form.DataSet.ContainsKey("legal-entity-data.strSubmissionLatePaymentFeeExempt"));
            Assert.That(form.DataSet["legal-entity-data.strSubmissionLatePaymentFeeExempt"], Is.EqualTo("False"));
            Assert.That(form.DataSet.ContainsKey("legal-entity-data.isActive"));
            Assert.That(form.DataSet["legal-entity-data.isActive"], Is.EqualTo("true"));
        }

        [Test()]
        public async Task StartSubmissionAsync_ReportId_Is_Set_To_SubmissionId()
        {
            // Arrange
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };

            // Act
            var response = await _submissionsManager.StartSubmissionAsync(model);

            // Assert
            var savedSubmission = await _submissionsRepository.FindFirstOrDefaultByConditionAsync(submission => true);

            var responseId = response.Id;
            responseId.Should().NotBeEmpty();
            savedSubmission.Id.Should().Be(responseId, "Id should have been saved in db");

            response.ReportId.Should().Be(responseId.ToString(), "correct Id should be returned");
            savedSubmission.ReportId.Should().Be(responseId.ToString(), "report Id should be set to string value of Id");
        }

        [Test]
        public async Task SearchSubmissionsAsync_NoCountry_IsEvaluatedAsEmptyString()
        {
            // Arrange
            // Create a submission with an empty country value
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to get the FormDocumentId
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Get the form document and add an attribute with empty country value
            var formDocument = await _formDocumentsRepository.GetByIdAsync(submissionEntity.FormDocumentId!.Value);
            formDocument.Attributes.Add(new FormDocumentAttribute("address-of-head-office.country", "address-of-head-office.country") { Value = "" });
            await _formDocumentsRepository.SaveChangesAsync();

            // Create search request with "no-country" filter
            var searchRequest = new SearchSubmissionsRequest
            {
                ModuleId = ModuleStrId,
                Country = "no-country",
                AuthorizedJurisdictionIDs = [JurisdictionNevisId],
                PagingInfo = new PagingInfo(1, 10)
            };

            // Act - Search for submissions with empty country
            var emptyCountryResult = await _submissionsManager.SearchSubmissionsAsync(searchRequest);

            // Assert - Should find the submission with empty country
            emptyCountryResult.Should().NotBeNull();
            emptyCountryResult.Count.Should().Be(1, "One submission with empty country should be found");
            emptyCountryResult[0].Id.Should().Be(submission.Id);

            // Now update the submission to have a country value
            var countryAttribute = formDocument.Attributes.Single(a => a.Key == "address-of-head-office.country");
            countryAttribute.Value = "KNA";
            await _formDocumentsRepository.SaveChangesAsync();

            // Act - Search again for submissions with empty country
            var updatedResult = await _submissionsManager.SearchSubmissionsAsync(searchRequest);

            // Assert - Should not find any submissions now
            updatedResult.Should().NotBeNull();
            updatedResult.Count.Should().Be(0, "No submissions with empty country should be found after updating the country value");
        }

        [Test]
        public async Task GetSubmissionsPaidStatusByCompanyAndYearAsync_ExcludesDeletedSubmissions()
        {
            // Arrange
            // Create two submissions for the same company and year
            var model1 = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission1 = await _submissionsManager.StartSubmissionAsync(model1);

            // Create a submission for the second legal entity
            var model2 = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntity2Id, ModuleId = ModuleStrId, FinancialYear = 2024 };
            await _submissionsManager.StartSubmissionAsync(model2);

            // Get the submission entity to mark it as deleted
            var submissionEntity1 = await _submissionsRepository.GetByIdAsync(submission1.Id);

            // Access the underlying DbContext to modify the entity directly
            var dbContext = _submissionsRepository.DbContext;

            // Mark the first submission as deleted
            submissionEntity1.Delete();
            await dbContext.SaveChangesAsync();

            // Create request for getting paid status
            var companyFilingYears = new List<CompanyFinancialYearDto>
            {
                new CompanyFinancialYearDto { CompanyVPCode = "1", FinancialYear = 2024 },
                new CompanyFinancialYearDto { CompanyVPCode = _nevisLegalEntity2VpCode, FinancialYear = 2024 }
            };

            // Act
            var result = await _submissionsManager.GetSubmissionsPaidStatusByCompanyAndYearAsync(
                companyFilingYears,
                ModuleStrId,
                new List<Guid> { JurisdictionNevisId });

            // Assert
            result.Should().NotBeNull();
            result.PaidStatuses.Should().NotBeNull();
            result.PaidStatuses.Should().HaveCount(2, "Both company/year pairs should be in the result");

            // Check company 1 (with deleted submission)
            var company1Status = result.PaidStatuses.FirstOrDefault(s => s.CompanyVPCode == "1");
            company1Status.Should().NotBeNull();
            company1Status!.SubmissionAvailable.Should().BeFalse("Deleted submission should not be considered available");

            // Check company 2 (with active submission)
            var company2Status = result.PaidStatuses.FirstOrDefault(s => s.CompanyVPCode == _nevisLegalEntity2VpCode);
            company2Status.Should().NotBeNull();
            company2Status!.SubmissionAvailable.Should().BeTrue("Non-deleted submission should be considered available");
        }

        [Test]
        public async Task MarkSubmissionsAsPaidAsync_DeletedSubmission_NotFound()
        {
            // Arrange
            // Create two submissions
            var model1 = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission1 = await _submissionsManager.StartSubmissionAsync(model1);

            // Get the first submission entity to mark it as deleted
            var submissionEntity1 = await _submissionsRepository.GetByIdAsync(submission1.Id);

            // Access the underlying DbContext to modify the entity directly
            var dbContext = _submissionsRepository.DbContext;

            // Mark the first submission as deleted
            submissionEntity1.Delete();
            await dbContext.SaveChangesAsync();

            // Create a list of submission IDs including the deleted submission ID
            var submissionIds = new List<Guid> { submission1.Id };

            // Act
            var result = await _submissionsManager.MarkSubmissionsAsPaidAsync(
                submissionIds,
                true, // Mark as paid
                new List<Guid> { JurisdictionNevisId });

            // Assert
            result.Should().NotBeNull();
            result.Results.Should().NotBeNull();
            (Guid _, MarkSubmissionsAsPaidResultDTO submissionResult) = result.Results.Should().ContainSingle("Submission should be in the result").Which;

            // Check deleted submission result
            var deletedSubmissionResult = submissionResult;
            deletedSubmissionResult.Should().NotBeNull();
            deletedSubmissionResult.ErrorMessage.Should().NotBeNullOrEmpty("Deleted submission should have an error message");
            deletedSubmissionResult.ErrorMessage.Should()
                                   .Contain("not found", "Error should indicate the submission was not found");
        }

        [Test]
        public async Task MarkSubmissionsAsPaidByCompanyAndYearAsync_DeletedSubmission_NotFound()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Access the underlying DbContext to modify the entity directly
            var dbContext = _submissionsRepository.DbContext;

            // Mark the submission as deleted
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create company/year pair for the deleted submission
            var companyFilingYears = new List<CompanyFinancialYearDto>
            {
                new CompanyFinancialYearDto { CompanyVPCode = "1", FinancialYear = 2024 }
            };

            // Act
            var result = await _submissionsManager.MarkSubmissionsAsPaidByCompanyAndYearAsync(
                companyFilingYears,
                true, // Mark as paid
                ModuleStrId,
                new List<Guid> { JurisdictionNevisId });

            // Assert
            result.Should().NotBeNull();
            result.Results.Should().NotBeNull();
            result.Results.Should().HaveCount(1, "One company/year pair should be in the result");

            // Check company result (with deleted submission)
            var companyResult = result.Results.Single();
            companyResult.Should().NotBeNull();
            companyResult.CompanyVPCode.Should().Be("1", "Company code should match");
            companyResult.FinancialYear.Should().Be(2024, "Financial year should match");
            companyResult.ErrorMessage.Should().NotBeNullOrEmpty("Company with deleted submission should have an error message");
            companyResult.ErrorMessage.Should().Contain("not found", "Error should indicate the submission was not found");
        }

        [Test]
        public async Task ListSubmissionsAsync_ExcludesDeletedSubmissions()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create request for listing submissions
            var request = new ListSubmissionsRequest
            {
                LegalEntityId = _nevisLegalEntityId,
                ModuleId = ModuleStrId,
                PagingInfo = new PagingInfo(1, 10)
            };

            // Act
            var result = await _submissionsManager.ListSubmissionsAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Count.Should().Be(0, "Deleted submissions should be excluded from the results");
        }

        [Test]
        public async Task GetSubmissionAsync_DeletedSubmission_ThrowsNotFoundException()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Act & Assert
            var result = await _submissionsManager.GetSubmissionAsync(submission.Id);
            result.Should().BeNull("Deleted submissions should not be found");
        }

        [Test]
        public async Task GetSubmissionsAsync_IncludesDeletedSubmissions()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Act
            var result = await _submissionsManager.GetSubmissionsAsync(new List<Guid> { submission.Id });

            // Assert
            result.Should().NotBeNull();
            result.Should().NotBeEmpty("Deleted submissions should be included from results");
        }

        [Test]
        public async Task UpdateSubmissionExport_ExcludesDeletedSubmissions()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Act - This should not throw an exception, but simply ignore the deleted submission
            await _submissionsManager.UpdateSubmissionExport(
                new List<Guid> { submission.Id },
                Guid.NewGuid(), // userId
                ModuleStrId);

            // Assert - Get the submission using the including deleted repository to verify it wasn't modified
            var submissionsIncludingDeletedRepository = _server.Services.GetRequiredService<ISubmissionsIncludingDeletedRepository>();
            var deletedSubmission = await submissionsIncludingDeletedRepository.GetByIdAsync(submission.Id);

            deletedSubmission.Should().NotBeNull();
            deletedSubmission.IsDeleted.Should().BeTrue("Submission should still be marked as deleted");
            deletedSubmission.ExportedBy.Should().BeNull("Deleted submission should not have been updated with export information");
            deletedSubmission.ExportedAt.Should().BeNull("Deleted submission should not have been updated with export information");
        }

        [Test]
        public async Task FilterPanamaSubmissionsForReportAsync_ExcludesDeletedSubmissions()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _panamaLegalEntityId, ModuleId = base.ModuleBfrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create request for filtering submissions
            var request = new FilterSubmissionsRequest
            {
                ModuleId = base.ModuleBfrId,
                AuthorizedJurisdictionIDs = new List<Guid>
                {
                    JurisdictionNevisId
                }
            };

            // Act
            var result = await _submissionsManager.FilterPanamaSubmissionsForReportAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty("Deleted submissions should be excluded from filter results");
        }

        [Test]
        public async Task FilterBahamasSubmissionsForReportAsync_ExcludesDeletedSubmissions()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _bahamasLegalEntityId, ModuleId = base.ModuleEsId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create request for filtering submissions
            var request = new SearchSubmissionsRequest
            {
                ModuleId = base.ModuleEsId,
                FinancialYear = 2024,
                AuthorizedJurisdictionIDs = new List<Guid>
                {
                    JurisdictionNevisId
                }
            };

            // Act
            var result = await _submissionsManager.FilterBahamasSubmissionsForReportAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty("Deleted submissions should be excluded from filter results");
        }

        [Test]
        public async Task RetrieveSubmissionsByIdsAsync_IncludeDeletedFalse_ExcludesDeletedSubmissions()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create request for retrieving submissions with IncludeDeleted = false (default)
            var request = new RetrieveSubmissionsRequest
            {
                SubmissionIds = [submission.Id],
                AuthorizedJurisdictionIDs = [JurisdictionNevisId],
                IncludeDeleted = false
            };

            // Act
            var result = await _submissionsManager.RetrieveSubmissionsByIdsAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty("Deleted submissions should be excluded from retrieval results when IncludeDeleted is false");
        }

        [Test]
        public async Task RetrieveSubmissionsByIdsAsync_IncludeDeletedTrue_IncludesDeletedSubmissions()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create request for retrieving submissions with IncludeDeleted = true
            var request = new RetrieveSubmissionsRequest
            {
                SubmissionIds = [submission.Id],
                AuthorizedJurisdictionIDs = [JurisdictionNevisId],
                IncludeDeleted = true
            };

            // Act
            var result = await _submissionsManager.RetrieveSubmissionsByIdsAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Should().NotBeEmpty("Deleted submissions should be included in retrieval results when IncludeDeleted is true");
            result.Should().HaveCount(1, "One deleted submission should be retrieved");
            result[0].Id.Should().Be(submission.Id);
            result[0].IsDeleted.Should().BeTrue("Submission should be marked as deleted");
        }

        [Test]
        public async Task GetSubmissionAsync_WithIncludeFormDocument_DeletedSubmission_ThrowsNotFoundException()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Act & Assert
            Func<Task> act = async () => await _submissionsManager.GetSubmissionAsync(submission.Id, true);
            await act.Should().ThrowAsync<NotFoundException>("Deleted submissions should not be found when allowDeleted is false");
        }

        [Test]
        public async Task GetSubmissionAsync_WithIncludeFormDocument_AllowDeleted_ReturnsDeletedSubmission()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Act
            var result = await _submissionsManager.GetSubmissionAsync(submission.Id, true, true);

            // Assert
            result.Should().NotBeNull("Deleted submission should be returned when allowDeleted is true");
            result.Id.Should().Be(submission.Id);
            result.IsDeleted.Should().BeTrue("Submission should be marked as deleted");
        }

        [Test]
        public async Task ListSubmissionsByMasterClientAsync_ExcludesDeletedSubmissions()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the master client ID
            var legalEntity = await _legalEntitiesRepository.GetByIdAsync(_nevisLegalEntityId);
            var masterClientId = legalEntity.MasterClientId;

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create request for listing submissions by master client
            var request = new ListSubmissionsByMasterClientRequest
            {
                MasterClientId = masterClientId,
                PagingInfo = new PagingInfo(1, 10)
            };

            // Act
            var result = await _submissionsManager.ListSubmissionsAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Count.Should().Be(0, "Deleted submissions should be excluded from the results");
        }

        [Test]
        public async Task SearchSubmissionsAsync_IsDeletedFalse_ExcludesDeletedSubmissions()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create search request with IsDeleted = false (default)
            var searchRequest = new SearchSubmissionsRequest
            {
                ModuleId = ModuleStrId,
                AuthorizedJurisdictionIDs = [JurisdictionNevisId],
                PagingInfo = new PagingInfo(1, 10),
                IsDeleted = false
            };

            // Act
            var result = await _submissionsManager.SearchSubmissionsAsync(searchRequest);

            // Assert
            result.Should().NotBeNull();
            result.Count.Should().Be(0, "Deleted submissions should be excluded from search results when IsDeleted is false");
        }

        [Test]
        public async Task SearchSubmissionsAsync_IsDeletedTrue_IncludesDeletedSubmissions()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create search request with IsDeleted = true
            var searchRequest = new SearchSubmissionsRequest
            {
                ModuleId = ModuleStrId,
                AuthorizedJurisdictionIDs = [JurisdictionNevisId],
                PagingInfo = new PagingInfo(1, 10),
                IsDeleted = true
            };

            // Act
            var result = await _submissionsManager.SearchSubmissionsAsync(searchRequest);

            // Assert
            result.Should().NotBeNull();
            result.Count.Should().Be(1, "Deleted submissions should be included in search results when isDeleted is true");
            result[0].Id.Should().Be(submission.Id);
            result[0].IsDeleted.Should().BeTrue("Submission should be marked as deleted");
        }

        [Test]
        public async Task FilterNevisSubmissionsForReportAsync_ExcludesDeletedSubmissions()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create request for filtering submissions
            var request = new SearchSubmissionsRequest
            {
                ModuleId = ModuleStrId,
                FinancialYear = 2024,
                AuthorizedJurisdictionIDs = [JurisdictionNevisId]
            };

            // Act
            var result = await _submissionsManager.FilterNevisSubmissionsForReportAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty("Deleted submissions should be excluded from filter results");
        }

        [Test]
        public async Task ReopenSubmissionAsync_DeletedSubmission_ThrowsNotFoundException()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Submit the submission to make it eligible for reopening
            await _submissionsManager.SubmitSubmissionAsync(new SubmitSubmissionDTO { SubmissionId = submission.Id });

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create reopen request
            var reopenRequest = new ReopenSubmissionDTO { SubmissionId = submission.Id };

            // Act & Assert
            Func<Task> act = async () => await _submissionsManager.ReopenSubmissionAsync(reopenRequest);
            await act.Should().ThrowAsync<NotFoundException>("Deleted submissions should not be found for reopening");
        }

        [Test]
        public async Task UpdateSubmissionDataSetAsync_DeletedSubmission_ThrowsNotFoundException()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create update request
            var updateRequest = new SubmissionDataSetDTO
            {
                Id = submission.Id,
                DataSet = new Dictionary<string, string> { { "test", "value" } }
            };

            // Act & Assert
            Func<Task> act = async () => await _submissionsManager.UpdateSubmissionDataSetAsync(updateRequest);
            await act.Should().ThrowAsync<NotFoundException>("Deleted submissions should not be found for updating");
        }

        [Test]
        public async Task SubmitSubmissionAsync_DeletedSubmission_ThrowsNotFoundException()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create submit request
            var submitRequest = new SubmitSubmissionDTO { SubmissionId = submission.Id };

            // Act & Assert
            Func<Task> act = async () => await _submissionsManager.SubmitSubmissionAsync(submitRequest);
            await act.Should().ThrowAsync<NotFoundException>("Deleted submissions should not be found for submission");
        }

        [Test]
        public async Task GetAvailableSubmissionYears_ExcludesDeletedSubmissions()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create request for available years
            var request = new AvailableSubmissionYearsRequest
            {
                LegalEntityId = _nevisLegalEntityId,
                ModuleId = ModuleStrId
            };

            // Act
            var result = await _submissionsManager.GetAvailableSubmissionYears(request);

            // Assert
            result.Should().NotBeNull();
            result.Years.Should().Contain(2024, "Deleted submissions should not affect available years");
        }

        [Test]
        public async Task GetModuleKeyForSubmissionAsync_DeletedSubmission_ThrowsNotFoundException()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Act & Assert
            var result = await _submissionsManager.GetModuleKeyForSubmissionAsync(submission.Id);
            result.Should().NotBeNull();
            result.Should().Be("SimplifiedTaxReturn", "Deleted submissions should be found for getting module key");
        }

        [Test]
        public async Task UpdateSubmissionGeneralInformationAsync_DeletedSubmission_ThrowsNotFoundException()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create update request
            var updateRequest = new UpdateSubmissionInformationDTO
            {
                StartAt = new DateTime(2025, 1, 1),
                EndAt = new DateTime(2025, 12, 31)
            };

            // Act & Assert
            Func<Task> act = async () => await _submissionsManager.UpdateSubmissionGeneralInformationAsync(submission.Id, updateRequest, true);
            await act.Should().ThrowAsync<NotFoundException>("Deleted submissions should not be found for updating general information");
        }

        [Test]
        public async Task UpdateSubmissionGeneralInformationManagementAsync_DeletedSubmission_ThrowsNotFoundException()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create update request
            var updateRequest = new UpdateSubmissionInformationDTO
            {
                StartAt = new DateTime(2025, 1, 1),
                EndAt = new DateTime(2025, 12, 31)
            };

            // Act & Assert
            Func<Task> act = async () => await _submissionsManager.UpdateSubmissionGeneralInformationManagementAsync(submission.Id, updateRequest, true);
            await act.Should().ThrowAsync<NotFoundException>("Deleted submissions should not be found for updating general information");
        }

        [Test]
        public async Task SearchSubmissionsForPanamaAsync_IsDeletedNull_IncludesDeletedSubmissions()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _panamaLegalEntityId, ModuleId = base.ModuleBfrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Update the submission so that status is not Temporal
            await _submissionsManager.UpdateSubmissionGeneralInformationAsync(
                submission.Id,
                new UpdateSubmissionInformationDTO
                {
                    StartAt = _submissionStartAt,
                    EndAt = _submissionEndAt
                },
                true);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create request for filtering submissions with IsDeleted = null
            var request = new FilterSubmissionsRequest
            {
                ModuleId = base.ModuleBfrId,
                AuthorizedJurisdictionIDs = [JurisdictionPanamaId],
                IsDeleted = null
            };

            // Act
            var result = await _submissionsManager.SearchSubmissionsForPanamaAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Count.Should().Be(1, "Deleted submissions should be included in search results when IsDeleted is null");
            result[0].Id.Should().Be(submission.Id);
            result[0].IsDeleted.Should().BeTrue("Submission should be marked as deleted");
        }

        [Test]
        public async Task SearchSubmissionsForPanamaAsync_IsDeletedTrue_IncludesDeletedSubmissions()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _panamaLegalEntityId, ModuleId = base.ModuleBfrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Update the submission so that status is not Temporal
            await _submissionsManager.UpdateSubmissionGeneralInformationAsync(
                submission.Id,
                new UpdateSubmissionInformationDTO
                {
                    StartAt = _submissionStartAt,
                    EndAt = _submissionEndAt
                },
                true);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create request for filtering submissions with IsDeleted = true
            var request = new FilterSubmissionsRequest
            {
                ModuleId = base.ModuleBfrId,
                AuthorizedJurisdictionIDs = [JurisdictionPanamaId],
                IsDeleted = true
            };

            // Act
            var result = await _submissionsManager.SearchSubmissionsForPanamaAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Count.Should().Be(1, "Deleted submissions should be included in search results when IsDeleted is true");
            result[0].Id.Should().Be(submission.Id);
            result[0].IsDeleted.Should().BeTrue("Submission should be marked as deleted");
        }

        [Test]
        public async Task SearchSubmissionsForPanamaAsync_IsDeletedFalse_ExcludesDeletedSubmissions()
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _panamaLegalEntityId, ModuleId = base.ModuleBfrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create request for filtering submissions
            var request = new FilterSubmissionsRequest
            {
                ModuleId = base.ModuleBfrId,
                AuthorizedJurisdictionIDs = [JurisdictionNevisId],
                IsDeleted = false
            };

            // Act
            var result = await _submissionsManager.SearchSubmissionsForPanamaAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Count.Should().Be(0, "Deleted submissions should be excluded from search results");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_IsDeletedFalse_ExcludesDeletedSubmissions()
        {
            // Arrange
            await CreateAndRetrieveBahamasSubmission();

            // Create request for filtering submissions with IsDeleted = false (default)
            var request = new FilterSubmissionsRequestForBahamas
            {
                ModuleId = base.ModuleEsId,
                AuthorizedJurisdictionIDs = [JurisdictionNevisId],
                IsDeleted = false
            };

            // Act
            var result = await _submissionsManager.SearchSubmissionsForBahamasAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Count.Should().Be(0, "Deleted submissions should be excluded from search results when IsDeleted is false");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_IsDeletedTrue_IncludesDeletedSubmissions()
        {
            // Arrange
            var (submission, _) = await CreateAndRetrieveBahamasSubmission();

            // Create request for filtering submissions with IsDeleted = true
            var request = new FilterSubmissionsRequestForBahamas
            {
                ModuleId = base.ModuleEsId,
                AuthorizedJurisdictionIDs = [JurisdictionBahamasId],
                IsDeleted = true
            };

            // Act
            var result = await _submissionsManager.SearchSubmissionsForBahamasAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Count.Should().Be(1, "Deleted submissions should be included in search results when IsDeleted is true");
            result[0].Id.Should().Be(submission.Id);
            // Note: ListSubmissionBahamasDTO doesn't have an IsDeleted property
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_IsDeletedNull_IncludesDeletedSubmissions()
        {
            // Arrange
            var (submission, _) = await CreateAndRetrieveBahamasSubmission();

            // Create request for filtering submissions with IsDeleted = true
            var request = new FilterSubmissionsRequestForBahamas
            {
                ModuleId = base.ModuleEsId,
                AuthorizedJurisdictionIDs = [JurisdictionBahamasId],
                IsDeleted = null
            };

            // Act
            var result = await _submissionsManager.SearchSubmissionsForBahamasAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Count.Should().Be(1, "Deleted submissions should be included in search results when IsDeleted is true");
            result[0].Id.Should().Be(submission.Id);
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_StatusesSingleStatus_ReturnsOnlyThatStatus()
        {
            // Arrange
            var draftLegalEntity = await CreateBahamasLegalEntity("Draft Company", "DRAFT001", "DRAFT-001");
            var submittedLegalEntity = await CreateBahamasLegalEntity("Submitted Company", "SUBMIT001", "SUBMIT-001");

            // Create submissions with different statuses using different legal entities
            var draftSubmission = await CreateBahamasSubmission(legalEntityId: draftLegalEntity.Id, submit: false, delete: false, datasetUpdates: null, export: false, reopen: false, resubmit: false);
            var submittedSubmission = await CreateBahamasSubmission(legalEntityId: submittedLegalEntity.Id, submit: true, delete: false, datasetUpdates: null, export: false, reopen: false, resubmit: false);

            // Create request filtering for only Draft status
            var request = new FilterSubmissionsRequestForBahamas
            {
                ModuleId = base.ModuleEsId,
                AuthorizedJurisdictionIDs = [JurisdictionBahamasId],
                Statuses = [SubmissionStatus.Draft]
            };

            // Act
            var result = await _submissionsManager.SearchSubmissionsForBahamasAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Should().ContainSingle().Which.Id.Should().Be(draftSubmission.Id, "The returned submission should be the draft submission");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_StatusesMultipleStatuses_ReturnsOnlyThoseStatuses()
        {
            // Arrange
            // Create additional legal entities for this test to avoid conflicts
            var draftLegalEntity = await CreateBahamasLegalEntity("Multi Draft Company", "MDRAFT001", "MDRAFT-001");
            var submittedLegalEntity = await CreateBahamasLegalEntity("Multi Submitted Company", "MSUBMIT001", "MSUBMIT-001");
            var revisionLegalEntity = await CreateBahamasLegalEntity("Multi Revision Company", "MREVISION001", "MREVISION-001");

            // Create submissions with different statuses using different legal entities
            var draftSubmission = await CreateBahamasSubmission(legalEntityId: draftLegalEntity.Id, submit: false, delete: false, datasetUpdates: null, export: false, reopen: false, resubmit: false);
            var submittedSubmission = await CreateBahamasSubmission(legalEntityId: submittedLegalEntity.Id, submit: true, delete: false, datasetUpdates: null, export: false, reopen: false, resubmit: false);
            var revisionSubmission = await CreateBahamasSubmission(legalEntityId: revisionLegalEntity.Id, submit: true, delete: false, datasetUpdates: null, export: false, reopen: true, resubmit: false);

            // Create request filtering for both Draft and Submitted statuses
            var request = new FilterSubmissionsRequestForBahamas
            {
                ModuleId = base.ModuleEsId,
                AuthorizedJurisdictionIDs = [JurisdictionBahamasId],
                Statuses = [SubmissionStatus.Draft, SubmissionStatus.Submitted]
            };

            // Act
            var result = await _submissionsManager.SearchSubmissionsForBahamasAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2, "Only submissions with Draft and Submitted statuses should be returned");

            var returnedIds = result.Select(r => r.Id).ToList();
            returnedIds.Should().Contain(draftSubmission.Id, "Draft submission should be included");
            returnedIds.Should().Contain(submittedSubmission.Id, "Submitted submission should be included");
            returnedIds.Should().NotContain(revisionSubmission.Id, "Revision submission should be excluded");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_StatusesEmptyList_ReturnsAllSubmissions()
        {
            // Arrange
            // Create additional legal entities for this test to avoid conflicts
            var emptyDraftLegalEntity = await CreateBahamasLegalEntity("Empty Draft Company", "EDRAFT001", "EDRAFT-001");
            var emptySubmittedLegalEntity = await CreateBahamasLegalEntity("Empty Submitted Company", "ESUBMIT001", "ESUBMIT-001");

            // Create submissions with different statuses using different legal entities
            var draftSubmission = await CreateBahamasSubmission(legalEntityId: emptyDraftLegalEntity.Id, submit: false, delete: false, datasetUpdates: null, export: false, reopen: false, resubmit: false);
            var submittedSubmission = await CreateBahamasSubmission(legalEntityId: emptySubmittedLegalEntity.Id, submit: true, delete: false, datasetUpdates: null, export: false, reopen: false, resubmit: false);

            // Create request with empty status list
            var request = new FilterSubmissionsRequestForBahamas
            {
                ModuleId = base.ModuleEsId,
                AuthorizedJurisdictionIDs = [JurisdictionBahamasId],
                Statuses = new List<SubmissionStatus>()
            };

            // Act
            var result = await _submissionsManager.SearchSubmissionsForBahamasAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2, "All submissions should be returned when status list is empty");

            var returnedIds = result.Select(r => r.Id).ToList();
            returnedIds.Should().Contain(draftSubmission.Id, "Draft submission should be included");
            returnedIds.Should().Contain(submittedSubmission.Id, "Submitted submission should be included");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_StatusesNullList_ReturnsAllSubmissions()
        {
            // Arrange
            // Create additional legal entities for this test to avoid conflicts
            var nullDraftLegalEntity = await CreateBahamasLegalEntity("Null Draft Company", "NDRAFT001", "NDRAFT-001");
            var nullSubmittedLegalEntity = await CreateBahamasLegalEntity("Null Submitted Company", "NSUBMIT001", "NSUBMIT-001");

            // Create submissions with different statuses using different legal entities
            var draftSubmission = await CreateBahamasSubmission(legalEntityId: nullDraftLegalEntity.Id, submit: false, delete: false, datasetUpdates: null, export: false, reopen: false, resubmit: false);
            var submittedSubmission = await CreateBahamasSubmission(legalEntityId: nullSubmittedLegalEntity.Id, submit: true, delete: false, datasetUpdates: null, export: false, reopen: false, resubmit: false);

            // Create request with null status list
            var request = new FilterSubmissionsRequestForBahamas
            {
                ModuleId = base.ModuleEsId,
                AuthorizedJurisdictionIDs = [JurisdictionBahamasId],
                Statuses = null
            };

            // Act
            var result = await _submissionsManager.SearchSubmissionsForBahamasAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Count.Should().Be(2, "All submissions should be returned when status list is null");

            var returnedIds = result.Select(r => r.Id).ToList();
            returnedIds.Should().Contain(draftSubmission.Id, "Draft submission should be included");
            returnedIds.Should().Contain(submittedSubmission.Id, "Submitted submission should be included");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_MapsCreatedByEmail()
        {
            var (_, result) = await CreateAndRetrieveBahamasSubmission();

            // Assert
            result.CreatedByEmail.Should().Be(_clientUserEmail);
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_MapsCreatedAt()
        {
            var (_, result) = await CreateAndRetrieveBahamasSubmission();

            // Assert
            result.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_MapsInitialSubmittedAt()
        {
            // Arrange & Act
            var (_, result) = await CreateAndRetrieveBahamasSubmission();

            // Assert
            result.InitialSubmittedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1), "InitialSubmittedAt should match the current time when the submission was created");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_MapsIncorporationDate()
        {
            // Arrange & Act
            var (_, result) = await CreateAndRetrieveBahamasSubmission();

            // Assert
            result.IncorporationDate.Should().Be(_bahamasLegalEntityIncorporationDate, "IncorporationDate should match the legal entity's incorporation date");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_MapsIncorporationCode()
        {
            // Arrange & Act
            var (_, result) = await CreateAndRetrieveBahamasSubmission();

            // Assert
            result.IncorporationCode.Should().Be(_bahamasLegalEntityIncorporationNr, "IncorporationCode should match the legal entity's incorporation number");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_MapsFinancialPeriodStartsAt()
        {
            // Arrange & Act
            var (_, result) = await CreateAndRetrieveBahamasSubmission();

            // Assert
            result.FinancialPeriodStartsAt.Should().Be(_submissionStartAt, "FinancialPeriodStartsAt should match the submission's StartsAt property");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_MapsFinancialPeriodEndsAt()
        {
            // Arrange & Act
            var (_, result) = await CreateAndRetrieveBahamasSubmission();

            // Assert 
            result.FinancialPeriodEndsAt.Should().Be(_submissionEndAt, "FinancialPeriodEndsAt should match the submission's EndsAt property");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_MapsLegalEntityName()
        {
            // Arrange & Act
            var (_, result) = await CreateAndRetrieveBahamasSubmission();

            // Assert
            result.LegalEntityName.Should().Be(_bahamasLegalEntityName, "LegalEntityName should match the legal entity's name");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_MapsLegalEntityCode()
        {
            // Arrange & Act
            var (_, result) = await CreateAndRetrieveBahamasSubmission();

            // Assert
            result.LegalEntityCode.Should().Be(_bahamasLegalEntityLegacyCode, "LegalEntityCode should match the legal entity's legacy code");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_MapsLegalEntityVPCode()
        {
            // Arrange & Act
            var (_, result) = await CreateAndRetrieveBahamasSubmission();

            // Assert
            result.LegalEntityVPCode.Should().Be(_bahamasLegalEntityCode, "LegalEntityVPCode should match the legal entity's code");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_MapsMasterClientCode()
        {
            // Arrange & Act
            var (_, result) = await CreateAndRetrieveBahamasSubmission();

            // Assert
            // Get the legal entity to verify the master client code
            result.MasterClientCode.Should().Be(_masterClientCode, "MasterClientCode should match the master client's code");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_MapsLegalEntityReferralOffice()
        {
            // Arrange & Act
            var (_, result) = await CreateAndRetrieveBahamasSubmission();

            // Assert
            result.LegalEntityReferralOffice.Should().Be(_bahamasLegalEntityReferralOffice, "LegalEntityReferralOffice should match the legal entity's referral office");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_MapsReopenedAt()
        {
            // Arrange & Act
            var (_, result) = await CreateAndRetrieveBahamasSubmission(reopen: true, resubmit: true);

            // Assert
            result.ReopenedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1), "ReopenedAt should match the current time when the submission was reopened");
            result.ReopenedAt.Should().BeAfter(result.InitialSubmittedAt!.Value);
            result.ReopenedAt.Should().BeBefore(result.SubmittedAt!.Value, "ReopenedAt should be before SubmittedAt because it represents the time when the submission was reopened after being initially submitted");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_MapsReopenedAt_OnlyWhenReopened()
        {
            // Arrange & Act
            var (_, result) = await CreateAndRetrieveBahamasSubmission(reopen: false);

            // Assert
            result.ReopenedAt.Should().BeNull("ReopenedAt should be null when the submission was not reopened");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_MapsSubmittedAt()
        {
            // Arrange & Act
            var (_, result) = await CreateAndRetrieveBahamasSubmission(reopen: true, resubmit: true);

            // Assert
            result.SubmittedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1), "SubmittedAt should match the current time when the submission was created");
            result.SubmittedAt.Should().BeAfter(result.ReopenedAt!.Value, "SubmittedAt should be after ReopenedAt because it represents the most recent submit time");
            result.SubmittedAt.Should().BeAfter(result.InitialSubmittedAt!.Value, "SubmittedAt should be after InitialSubmittedAt because it represents the most recent submit time");
        }

        [Test]
        public async Task SearchSubmissionsForBahamasAsync_MapsHasActivityNone()
        {
            // Arrange
            // Create a submission
            var (_, result) = await CreateAndRetrieveBahamasSubmission(submit: false, delete: false,
                datasetUpdates: new Dictionary<string, string> { { WellKnownFormDocumentAttibuteKeys.RelevantActivities + WellKnownBahamasRelevantActivityKeys.None + WellKnownFormDocumentAttibuteKeys.Selected, "true" } });

            // Assert
            result.HasActivityNone.Should().BeTrue("HasActivityNone should be true when the relevant attribute is set to true");
        }

        private async Task<(Submission submission, ListSubmissionBahamasDTO result)> CreateAndRetrieveBahamasSubmission(
            bool submit = true,
            bool delete = false,
            Dictionary<string, string> datasetUpdates = null,
            bool export = true,
            bool reopen = false,
            bool resubmit = false)
        {
            Submission submissionEntity = await CreateBahamasSubmission(submit, delete, datasetUpdates, export, reopen, resubmit);

            // Create request for filtering submissions
            var request = new FilterSubmissionsRequestForBahamas
            {
                ModuleId = base.ModuleEsId,
                AuthorizedJurisdictionIDs = [JurisdictionBahamasId],
                IsDeleted = null
            };

            // Act
            var results = await _submissionsManager.SearchSubmissionsForBahamasAsync(request);
            var result = results.Should().ContainSingle().Subject;
            return (submissionEntity, result);
        }

        private async Task<Submission> CreateBahamasSubmission(bool submit,
            bool delete,
            Dictionary<string, string> datasetUpdates,
            bool export,
            bool reopen,
            bool resubmit,
            int financialYear = 2024,
            Guid? legalEntityId = null)
        {
            // Arrange
            // Create a submission with the Bahamas legal entity and Economic Substance module
            var model = new StartSubmissionDTO
            {
                LegalEntityId = legalEntityId ?? _bahamasLegalEntityId,
                ModuleId = base.ModuleEsId,
                FinancialYear = financialYear
            };

            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity for later use
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Update the submission with specific dates using the proper API
            // This sets the StartsAt and EndsAt properties directly on the submission entity
            await _submissionsManager.UpdateSubmissionGeneralInformationAsync(
                submission.Id,
                new UpdateSubmissionInformationDTO
                {
                    StartAt = _submissionStartAt,
                    EndAt = _submissionEndAt
                },
                true);

            if (datasetUpdates != null)
            {
                var dataSet = new Dictionary<string, string>(datasetUpdates);

                // Update the submission data using the proper API
                await _submissionsManager.UpdateSubmissionDataSetAsync(new SubmissionDataSetDTO
                {
                    Id = submission.Id,
                    DataSet = dataSet
                });
            }

            // Mark the submission as deleted if requested
            if (delete)
            {
                var dbContext = _submissionsRepository.DbContext;
                submissionEntity.Delete();
                await dbContext.SaveChangesAsync();
            }

            if (submit)
            {
                // Submit the submission
                await _submissionsManager.SubmitSubmissionAsync(new SubmitSubmissionDTO { SubmissionId = submission.Id });
            }

            if (reopen)
            {
                await _submissionsManager.ReopenSubmissionAsync(new ReopenSubmissionDTO()
                {
                    SubmissionId = submission.Id,
                    Comments = "test"
                });
            }

            if (resubmit)
            {
                // Resubmit the submission
                await _submissionsManager.SubmitSubmissionAsync(new SubmitSubmissionDTO { SubmissionId = submission.Id });
            }

            if (export)
            {
                await _submissionsManager.UpdateSubmissionExport([submission.Id], ClientUser.Id, base.ModuleEsId);
            }

            return submissionEntity;
        }

        private async Task<LegalEntity> CreateBahamasLegalEntity(string name, string code, string legacyCode)
        {
            // Get the client user
            var user = ClientUser;
            SetWorkContextUser(user);

            // Get the master client
            var masterClient = await _masterClientsRepository.FindFirstOrDefaultByConditionAsync(mc => mc.Code == _masterClientCode);

            // Create a legal entity for Bahamas
            var legalEntity = new LegalEntity
            {
                MasterClientId = masterClient.Id,
                Name = name,
                Code = code,
                LegacyCode = legacyCode,
                JurisdictionId = JurisdictionBahamasId,
                EntityType = LegalEntityType.Company,
                IncorporationNr = Guid.NewGuid().ToString("N")[..8], // Use unique incorporation number
                ReferralOffice = "test office",
                IncorporationDate = DateTime.Now.AddYears(-5)
            };

            await _legalEntitiesRepository.InsertAsync(legalEntity, true);
            return legalEntity;
        }

        [Test]
        [TestCase(false)]
        [TestCase(true)]
        [TestCase(null)]
        public async Task SearchSubmissionsForBahamasReportAsync_IgnoresIsDeleted_ExcludesDeletedSubmissions(bool? isDeleted)
        {
            // Arrange
            // Create a submission
            var model = new StartSubmissionDTO { LegalEntityId = _bahamasLegalEntityId, ModuleId = base.ModuleEsId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Get the submission entity to mark it as deleted
            var submissionEntity = await _submissionsRepository.GetByIdAsync(submission.Id);

            // Mark the submission as deleted
            var dbContext = _submissionsRepository.DbContext;
            submissionEntity.Delete();
            await dbContext.SaveChangesAsync();

            // Create request for filtering submissions with specified IsDeleted value
            var request = new FilterSubmissionsRequestForBahamas
            {
                ModuleId = base.ModuleEsId,
                AuthorizedJurisdictionIDs = [JurisdictionBahamasId],
                IsDeleted = isDeleted
            };

            // Act
            var result = await _submissionsManager.SearchSubmissionsForBahamasReportAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty("Deleted submissions should be excluded from search results for report.");
        }

        [Test]
        public async Task UpdateSubmissionGeneralInformationManagementAsync_ChangesFinancialPeriod()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            await SetUpUserRolesAsync(ManagementUser, new List<string> { WellKnownRoleNames.Bahamas_Owner });
            var oldStartDate = new DateTime(2019, 01, 01);
            var oldEndDate = new DateTime(2019, 12, 31);
            var newStartDate = new DateTime(2020, 01, 01);
            var expectedStartDate = "2020-01-01T00:00:00Z";
            var newEndDate = new DateTime(2020, 12, 31);
            var expectedEndDate = "2020-12-31T00:00:00Z";

            var model = new StartSubmissionDTO { LegalEntityId = _bahamasLegalEntityId, ModuleId = base.ModuleEsId, FinancialYear = 2024 };
            var startSubmissionDto = await _submissionsManager.StartSubmissionAsync(model);

            var dataset = new Dictionary<string, string>
            {
                { "financial-period.startDate", oldStartDate.ToString(GeneralConsts.DateTimeZoneFormat) },
                { "financial-period.endDate", oldEndDate.ToString(GeneralConsts.DateTimeZoneFormat) },
            };
            await _submissionsManager.UpdateSubmissionDataSetAsync(new SubmissionDataSetDTO { Id = startSubmissionDto.Id, DataSet = dataset });
            await _submissionsManager.UpdateSubmissionGeneralInformationAsync(startSubmissionDto.Id,
                new UpdateSubmissionInformationDTO { StartAt = oldStartDate, EndAt = oldEndDate });

            // Act
            var modelUpdate = new UpdateSubmissionInformationDTO { StartAt = newStartDate, EndAt = newEndDate };
            await _submissionsManager.UpdateSubmissionGeneralInformationManagementAsync(startSubmissionDto.Id, modelUpdate, true);

            // Retrieve the updated submission
            var submission = (await _submissionsRepository.FindByConditionAsync(s => s.Id == startSubmissionDto.Id,
                q => q.Include(s => s.FormDocument.FormDocumentRevisions)
                      .Include(s => s.FormDocument.Attributes)
                )).Single();

            // Assert
            submission.StartsAt.Should().Be(newStartDate, "Start date should match the updated start date");
            submission.EndsAt.Should().Be(newEndDate, "End date should match the updated end date");

            // Get the latest revision's form and cast to KeyValueForm
            var formBuilder = Forms.FormBuilder.FromJson(submission.FormDocument.FormDocumentRevisions.Single().DataAsJson);
            var form = formBuilder.Form as KeyValueForm;

            // Assert the start and end dates in the form's dataset
            form!.DataSet["financial-period.startDate"].Should().Be(expectedStartDate, "End date should match the updated start date");
            form.DataSet["financial-period.endDate"].Should().Be(expectedEndDate, "End date should match the updated end date");

            // Assert the start and end dates in the form document's attributes
            var attributes = submission.FormDocument.Attributes.ToList();
            attributes.Should().NotBeNull();

            attributes.GetAttributeValue<string>("financial-period.startDate").Should().Be(expectedStartDate, "Start date attribute should match the updated start date");
            attributes.GetAttributeValue<string>("financial-period.endDate").Should().Be(expectedEndDate, "End date attribute should match the updated start date");
        }

        [Test]
        public async Task UpdateSubmissionGeneralInformationManagementAsync_UpdatesRelevantActivities()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            await SetUpUserRolesAsync(ManagementUser, new List<string> { WellKnownRoleNames.Bahamas_Owner });
            var oldStartDate = new DateTime(2019, 01, 01);
            var oldEndDate = new DateTime(2019, 12, 31);
            var newStartDate = new DateTime(2020, 01, 01);
            var expectedStartDate = "2020-01-01T00:00:00Z";
            var newEndDate = new DateTime(2020, 12, 31);
            var expectedEndDate = "2020-12-31T00:00:00Z";

            var model = new StartSubmissionDTO { LegalEntityId = _bahamasLegalEntityId, ModuleId = base.ModuleEsId, FinancialYear = 2024 };
            var startSubmissionDto = await _submissionsManager.StartSubmissionAsync(model);

            var dataset = new Dictionary<string, string>
            {
                { "financial-period.startDate", oldStartDate.ToString(GeneralConsts.DateTimeZoneFormat) },
                { "financial-period.endDate", oldEndDate.ToString(GeneralConsts.DateTimeZoneFormat) },
                { "relevant-activity-declaration.relevantActivities.0.selected", "false" },
                { "relevant-activity-declaration.relevantActivities.0.carriedOnForOnlyPartOfFinancialPeriod", "false" },
                { "relevant-activity-declaration.relevantActivities.0.startDate", "null" },
                { "relevant-activity-declaration.relevantActivities.0.endDate", "null" },
                { "relevant-activity-declaration.relevantActivities.1.selected", "true" },
                { "relevant-activity-declaration.relevantActivities.1.carriedOnForOnlyPartOfFinancialPeriod", "false" },
                { "relevant-activity-declaration.relevantActivities.1.startDate", oldStartDate.ToString(GeneralConsts.DateTimeZoneFormat) },
                { "relevant-activity-declaration.relevantActivities.1.endDate", oldEndDate.ToString(GeneralConsts.DateTimeZoneFormat) }
            };
            await _submissionsManager.UpdateSubmissionDataSetAsync(new SubmissionDataSetDTO { Id = startSubmissionDto.Id, DataSet = dataset });

            await _submissionsManager.UpdateSubmissionGeneralInformationAsync(startSubmissionDto.Id,
                new UpdateSubmissionInformationDTO { StartAt = oldStartDate, EndAt = oldEndDate });

            // Act
            var modelUpdate = new UpdateSubmissionInformationDTO { StartAt = newStartDate, EndAt = newEndDate };
            await _submissionsManager.UpdateSubmissionGeneralInformationManagementAsync(startSubmissionDto.Id, modelUpdate, true);

            // Retrieve the updated submission
            var submission = (await _submissionsRepository.FindByConditionAsync(s => s.Id == startSubmissionDto.Id,
                q => q.Include(s => s.FormDocument.FormDocumentRevisions)
                      .Include(s => s.FormDocument.Attributes)
                )).Single();

            // Assert

            // Assert the start and end dates in the form values
            var formBuilder = Forms.FormBuilder.FromJson(submission.FormDocument.FormDocumentRevisions.Single().DataAsJson);
            var form = formBuilder.Form as KeyValueForm;

            //DataSet: Relevant activity declaration was not selected
            form!.DataSet["relevant-activity-declaration.relevantActivities.0.selected"].Should().Be("false", "The 'selected' value for activity 0 should be false, as it was not set to true in the submission.");
            form.DataSet["relevant-activity-declaration.relevantActivities.0.carriedOnForOnlyPartOfFinancialPeriod"].Should().Be("false", "The 'carriedOnForOnlyPartOfFinancialPeriod' value for activity 0 should be false, as it was not set to true in the submission.");
            form.DataSet["relevant-activity-declaration.relevantActivities.0.startDate"].Should().Be("null", "Start date should remain null for activity 0 as it was not set to a date in the dataset");
            form.DataSet["relevant-activity-declaration.relevantActivities.0.endDate"].Should().Be("null", "End date should be null for activity 0 as it was not set to a date in the dataset");

            //DataSet: Relevant activity declaration was selected
            form.DataSet["relevant-activity-declaration.relevantActivities.1.selected"].Should().Be("true", "The 'selected' value for activity 1 should be true, as it was set to true in the submission");
            form.DataSet["relevant-activity-declaration.relevantActivities.1.carriedOnForOnlyPartOfFinancialPeriod"].Should().Be("false", "The 'carriedOnForOnlyPartOfFinancialPeriod' value for activity 1 should be false, as it was not set to true in the submission.");
            form.DataSet["relevant-activity-declaration.relevantActivities.1.startDate"].Should().Be(expectedStartDate, "Start date should match the updated start date");
            form.DataSet["relevant-activity-declaration.relevantActivities.1.endDate"].Should().Be(expectedEndDate, "End date should match the updated end date");

            // Assert the start and end dates in the form document's attributes
            var attributes = submission.FormDocument.Attributes.ToList();
            attributes.Should().NotBeNull();

            //Attributes: Relevant activity declaration was not selected
            attributes.GetAttributeValue<string>("relevant-activity-declaration.relevantActivities.0.selected").Should().Be("false", "The 'selected' attribute for activity 0 should be false, as it was never explicitly set to true in the dataset.");
            attributes.GetAttributeValue<string>("relevant-activity-declaration.relevantActivities.0.carriedOnForOnlyPartOfFinancialPeriod").Should().Be("false", "The 'carriedOnForOnlyPartOfFinancialPeriod' attribute for activity 0 should be false, as it was never explicitly set to true in the dataset.");
            attributes.GetAttributeValue<string>("relevant-activity-declaration.relevantActivities.0.startDate").Should().Be("null", "Start date attribute for activity 0 should be null as it was not set to a date in the dataset");
            attributes.GetAttributeValue<string>("relevant-activity-declaration.relevantActivities.0.endDate").Should().Be("null", "End date attribute for activity 0 should be null as it was not set to a date in the dataset");

            //Attributes: Relevant activity declaration was selected
            attributes.GetAttributeValue<string>("relevant-activity-declaration.relevantActivities.1.selected").Should().Be("true", "The 'selected' attribute for activity 1 should be false, as it was never explicitly set to true in the dataset.");
            attributes.GetAttributeValue<string>("relevant-activity-declaration.relevantActivities.1.carriedOnForOnlyPartOfFinancialPeriod").Should().Be("false", "The 'carriedOnForOnlyPartOfFinancialPeriod' attribute for activity 1 should be false, as it was never explicitly set to true in the dataset.");
            attributes.GetAttributeValue<string>("relevant-activity-declaration.relevantActivities.1.startDate").Should().Be(expectedStartDate, "Start date attribute should match the updated start date");
            attributes.GetAttributeValue<string>("relevant-activity-declaration.relevantActivities.1.endDate").Should().Be(expectedEndDate, "End date attribute should match the updated end date");
        }

        [Test]
        public async Task UpdateSubmissionGeneralInformationManagementAsync_SubmissionHasPartialRelevantActivities_ThrowsInvalidSubmissionDatesException()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            await SetUpUserRolesAsync(ManagementUser, new List<string> { WellKnownRoleNames.Bahamas_Owner });
            var partialRelevantStartDate = new DateTime(2020, 01, 01);
            var partialRelevantEndDate = new DateTime(2020, 06, 30);
            var oldStartDate = new DateTime(2019, 01, 01);
            var oldEndDate = new DateTime(2019, 12, 31);
            var newStartDate = new DateTime(2020, 01, 01);
            var newEndDate = new DateTime(2020, 12, 31);

            var model = new StartSubmissionDTO { LegalEntityId = _bahamasLegalEntityId, ModuleId = base.ModuleEsId, FinancialYear = 2024 };
            var startSubmissionDto = await _submissionsManager.StartSubmissionAsync(model);

            var dataset = new Dictionary<string, string>
            {
                { "financial-period.startDate", oldStartDate.ToString(GeneralConsts.DateTimeZoneFormat) },
                { "financial-period.endDate", oldEndDate.ToString(GeneralConsts.DateTimeZoneFormat) },
                { "relevant-activity-declaration.relevantActivities.0.selected", "true" },
                { "relevant-activity-declaration.relevantActivities.0.carriedOnForOnlyPartOfFinancialPeriod", "true" },
                { "relevant-activity-declaration.relevantActivities.0.startDate", partialRelevantStartDate.ToString(GeneralConsts.DateTimeZoneFormat) },
                { "relevant-activity-declaration.relevantActivities.0.endDate", partialRelevantEndDate.ToString(GeneralConsts.DateTimeZoneFormat) },
                { "relevant-activity-declaration.relevantActivities.1.selected", "true" },
                { "relevant-activity-declaration.relevantActivities.1.carriedOnForOnlyPartOfFinancialPeriod", "false" },
                { "relevant-activity-declaration.relevantActivities.1.startDate", oldStartDate.ToString(GeneralConsts.DateTimeZoneFormat) },
                { "relevant-activity-declaration.relevantActivities.1.endDate", oldEndDate.ToString(GeneralConsts.DateTimeZoneFormat) }
            };

            await _submissionsManager.UpdateSubmissionDataSetAsync(new SubmissionDataSetDTO { Id = startSubmissionDto.Id, DataSet = dataset });

            await _submissionsManager.UpdateSubmissionGeneralInformationAsync(startSubmissionDto.Id,
                new UpdateSubmissionInformationDTO { StartAt = oldStartDate, EndAt = oldEndDate });

            // Act
            var modelUpdate = new UpdateSubmissionInformationDTO { StartAt = newStartDate, EndAt = newEndDate };
            Func<Task> act = async () => await _submissionsManager.UpdateSubmissionGeneralInformationManagementAsync(startSubmissionDto.Id, modelUpdate, true);

            // Assert
            await act.Should().ThrowAsync<PreconditionFailedException>("Submissions should not be able to update as relevant activity date is not equal to the submission date.");
        }

        [Test]
        public async Task ReopenSubmissionAsync_ResetsExportedAtToNull()
        {
            // Arrange
            var model = new StartSubmissionDTO { LegalEntityId = _nevisLegalEntityId, ModuleId = ModuleStrId, FinancialYear = 2024 };
            var submission = await _submissionsManager.StartSubmissionAsync(model);

            // Submit the submission
            await _submissionsManager.SubmitSubmissionAsync(new SubmitSubmissionDTO { SubmissionId = submission.Id });

            // Export the submission
            await _submissionsManager.UpdateSubmissionExport(new List<Guid> { submission.Id }, ClientUser.Id, ModuleStrId);

            // Act
            // Reopen the submission
            await _submissionsManager.ReopenSubmissionAsync(new ReopenSubmissionDTO { SubmissionId = submission.Id });

            // Assert
            // ExportedAt should be reset to null
            var reopenedSubmission = await _submissionsRepository.GetByIdAsync(submission.Id);
            reopenedSubmission.ExportedAt.Should().BeNull("ExportedAt should be reset to null after reopening the submission");
        }
    }
}
