﻿using FluentAssertions;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Trust.Application.Contracts.Users;
using NetProGroup.Trust.DataManager.Users;
using NetProGroup.Trust.DataManager.Users.RequestResponses;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    public class Test_Users : TestBase
    {

        private IUsersDataManager _usersDataManager;
        private IUserManager _userManager;
        private IUserRepository _userRepository;

        private Guid _userId = UserConsts.SystemUserId;
        private List<ApplicationUserDTO> _users;

        /// <summary>
        /// Setup the test.
        /// </summary>
        /// <returns></returns>
        [SetUp]
        public async Task Setup()
        {
            _usersDataManager = _server.Services.GetRequiredService<IUsersDataManager>();
            _userManager = _server.Services.GetRequiredService<IUserManager>();
            _userRepository = _server.Services.GetRequiredService<IUserRepository>();
            await Seed();
        }

        public async Task Seed()
        {
            var registrationData = new List<(string FirstName, string LastName, string Email, Guid RoleId)>
            {
                ("Emily", "Smith", "<EMAIL>", _basicUserRoleId),
                ("Michael", "Johnson", "<EMAIL>", _superAdminRoleId),
                ("Olivia", "Williams", "<EMAIL>", _basicUserRoleId),
                ("James", "Brown", "<EMAIL>", _superAdminRoleId),
                ("Sophia", "Davis", "<EMAIL>", _basicUserRoleId),
            };

            _users = new List<ApplicationUserDTO>();

            foreach (var (firstName, lastName, email, roleId) in registrationData)
            {
                var user = await _userManager.CreateUserWithRolesAsync(
                    new RegistrationDTO
                    {
                        FirstName = firstName,
                        LastName = lastName,
                        UserName = email,
                        DisplayName = $"{firstName} {lastName}",
                        Email = email,
                        ObjectId = Guid.NewGuid(),
                        RoleIds = new List<Guid> { roleId }
                    });

                _users.Add(user);
            }
        }

        /// <summary>
        /// Get MFA info using authenticator setting.
        /// </summary>
        /// <returns></returns>
        [Test]
        public async Task List_GetMFAInfo_Authenticator()
        {
            // Arrange
            var request = new GetUserMFARequest() { UserId = _userId };

            // Act
            await _usersDataManager.SetMFAMethodAsync(_userId, MFAMethodConsts.Authenticator);
            var response = await _usersDataManager.GetUserMFAInfoAsync(request);

            // Assert
            //Assert.That(response, Is.Not.Null);

        }

        /// <summary>
        /// Get MFA info using emailcode setting.
        /// </summary>
        /// <returns></returns>
        [Test]
        public async Task List_GetMFAInfo_EmailCode()
        {
            // Arrange
            var request = new GetUserMFARequest() { UserId = _userId };

            // Act
            await _usersDataManager.SetMFAMethodAsync(_userId, MFAMethodConsts.EmailCode);
            var response = await _usersDataManager.GetUserMFAInfoAsync(request);

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.MFAEmailCodeExpiresIn, Is.EqualTo(300));
        }

        [Test]
        public async Task Create_MFAResetRequest_Success()
        {
            // Arrange
            var request = new MFAResetRequest() { UserId = _userId };

            // Act
            var response = await _usersDataManager.RequestMFAResetAsync(request);

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.MFAEmailCodeExpiresIn, Is.EqualTo(300));
            Assert.That(response.MFAEmailCode, Is.Not.Empty.Or.Null);
        }

        [Test]
        public async Task Confirm_MFAResetRequest_Success()
        {
            // Arrange
            var request = new MFAResetRequest() { UserId = _userId };

            // Act
            var response1 = await _usersDataManager.RequestMFAResetAsync(request);

            var response = await _usersDataManager.ConfirmMFAResetAsync(new ConfirmMFAResetRequest { UserId = _userId, ResponseCode = response1.MFAEmailCode });

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.Success, Is.EqualTo(true));
        }

        [Test]
        public async Task Confirm_MFAResetRequest_WrongCode_Fails()
        {
            // Arrange
            var request = new MFAResetRequest() { UserId = _userId };

            // Act
            var response1 = await _usersDataManager.RequestMFAResetAsync(request);

            var response = await _usersDataManager.ConfirmMFAResetAsync(new ConfirmMFAResetRequest { UserId = _userId, ResponseCode = response1.MFAEmailCode + "_Invalid" });

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.Success, Is.EqualTo(false));
        }

        [Test]
        public async Task Confirm_MFAResetRequest_Expired_Fails()
        {
            // Arrange
            var request = new MFAResetRequest() { UserId = _userId };

            // Act
            var response1 = await _usersDataManager.RequestMFAResetAsync(request);
            await _usersDataManager.SetAttributeValueAsync<DateTime>(_userId, UserAttributeKeys.MFAResetEmailCodeExpiration, DateTime.UtcNow.AddSeconds(-1), saveChanges: true);

            var response = await _usersDataManager.ConfirmMFAResetAsync(new ConfirmMFAResetRequest { UserId = _userId, ResponseCode = response1.MFAEmailCode });

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.Success, Is.EqualTo(false));
        }

        [Test]
        public async Task ListUsers_SortingByEmailAscending_SortsCorrectly()
        {
            // Arrange
            var request = new UsersRequestDTO
            {
                SortBy = "Email",
                SortOrder = OrderByDefines.Ascending,
                PageNumber = 1,
                PageSize = 20
            };

            // Act
            var result = await _usersDataManager.ListUsersAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCountGreaterThan(1);
            result.Should().BeInAscendingOrder(u => u.Email);
        }

        [Test]
        public async Task ListUsers_SortingByLockoutEnabled_SortsCorrectly()
        {
            // Arrange
            var userIdsToLock = new[] { _users[1].Id, _users[2].Id };
            foreach (var userId in userIdsToLock)
            {
                var user = await _userRepository.FindByUserByPredicateAsync(x => x.Id == userId);
                user.LockoutEnabled = true;
                await _userRepository.UpdateUserAsync(user);
            }

            var request = new UsersRequestDTO
            {
                SortBy = "IsBlocked",
                SortOrder = OrderByDefines.Descending,
                PageNumber = 1,
                PageSize = 20
            };

            // Act
            var result = await _usersDataManager.ListUsersAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCountGreaterThan(1);
            result.Should().BeInDescendingOrder(u => u.IsBlocked);
        }

        [Test]
        public async Task ListUsers_SortingByPrimaryRoleLabel_SortsCorrectly()
        {
            // Arrange
            var request = new UsersRequestDTO
            {
                SortBy = "PrimaryRoleLabel",
                SortOrder = OrderByDefines.Ascending,
                PageNumber = 1,
                PageSize = 20
            };

            // Act
            var result = await _usersDataManager.ListUsersAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCountGreaterThan(1);
            result.Should().BeInAscendingOrder(u => u.PrimaryRoleLabel);
        }

        [Test()]
        public void GetUserByIdAsync_ReturnsUser()
        {
            // Arrange
            var userId = _users[0].Id;

            // Act
            var result = _usersDataManager.GetUserByIdAsync(userId).Result;

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be(userId);
        }

    }
}
